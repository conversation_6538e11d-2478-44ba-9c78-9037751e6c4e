"use client";
import React from 'react';

interface VideoTutorial {
  id: string;
  title: string;
  thumbnail: string;
  duration?: string;
}

const videoTutorials: VideoTutorial[] = [
  {
    id: '1',
    title: 'Getting Started with...',
    thumbnail: 'gradient-1',
  },
  {
    id: '2',
    title: 'How to Create Your First AI Video',
    thumbnail: 'gradient-2',
  },
  {
    id: '3',
    title: 'Mastering Text-to-Image Gener......',
    thumbnail: 'gradient-3',
  },
  {
    id: '4',
    title: 'Using AI Voiceovers for Your...',
    thumbnail: 'gradient-4',
  },
  {
    id: '5',
    title: 'Mastering Text-to-Image GProj...',
    thumbnail: 'gradient-5',
  },
  {
    id: '6',
    title: 'How to Create Your First AI Video',
    thumbnail: 'gradient-6',
  },
  {
    id: '7',
    title: 'Getting Started with...',
    thumbnail: 'gradient-1',
  },
  {
    id: '8',
    title: 'How to Create Your First AI Video',
    thumbnail: 'gradient-2',
  },
  {
    id: '9',
    title: 'Mastering Text-to-Image Gener......',
    thumbnail: 'gradient-3',
  },
];

const HeartIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" stroke="white" strokeWidth="2" fill="none" />
  </svg>
);

const DownloadIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="white" strokeWidth="2" />
    <polyline points="7,10 12,15 17,10" stroke="white" strokeWidth="2" fill="none" />
    <line x1="12" y1="15" x2="12" y2="3" stroke="white" strokeWidth="2" />
  </svg>
);

const PlayIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
    <polygon points="5,3 19,12 5,21" fill="white" />
  </svg>
);

const SearchIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="11" cy="11" r="8" stroke="white" strokeWidth="2" />
    <path d="m21 21-4.35-4.35" stroke="white" strokeWidth="2" />
  </svg>
);

export default function LearningResources() {
  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-white mb-2">Learning Resources</h1>
        <p className="text-[#9ca3af] text-sm">Access tutorials, guides, and educational content.</p>
      </div>

      {/* Search Bar */}
      <div className="mb-8">
        <div className="relative max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon />
          </div>
          <input
            type="text"
            placeholder="Search tutorials..."
            className="w-full pl-10 pr-4 py-3 bg-[#8B7CF8] bg-opacity-20 border border-[#8B7CF8] border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-[#8B7CF8] focus:border-transparent"
          />
        </div>
      </div>

      {/* Video Grid */}
      <div className="grid grid-cols-3 gap-6">
        {videoTutorials.map((video) => {
          const getGradientClass = (thumbnail: string) => {
            switch (thumbnail) {
              case 'gradient-1':
                return 'bg-gradient-to-br from-orange-400 via-red-500 to-pink-500';
              case 'gradient-2':
                return 'bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600';
              case 'gradient-3':
                return 'bg-gradient-to-br from-pink-400 via-purple-500 to-indigo-600';
              case 'gradient-4':
                return 'bg-gradient-to-br from-yellow-400 via-orange-500 to-red-600';
              case 'gradient-5':
                return 'bg-gradient-to-br from-green-400 via-blue-500 to-purple-600';
              case 'gradient-6':
                return 'bg-gradient-to-br from-indigo-400 via-purple-500 to-pink-600';
              default:
                return 'bg-gradient-to-br from-gray-400 via-gray-500 to-gray-600';
            }
          };

          return (
            <div key={video.id} className="group cursor-pointer">
              <div className="relative bg-[#1a1f1e] rounded-lg overflow-hidden border border-[#2a2f2e] hover:border-[#3a3f3e] transition-colors">
                {/* Thumbnail */}
                <div className="relative aspect-video">
                  {/* Gradient Background */}
                  <div className={`absolute inset-0 ${getGradientClass(video.thumbnail)}`}></div>

                  {/* Action Icons */}
                  <div className="absolute top-3 right-3 flex gap-2">
                    <button className="p-2 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-all">
                      <HeartIcon />
                    </button>
                    <button className="p-2 bg-black bg-opacity-50 rounded-full hover:bg-opacity-70 transition-all">
                      <DownloadIcon />
                    </button>
                  </div>

                  {/* Play Button */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <button className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all backdrop-blur-sm">
                      <PlayIcon />
                    </button>
                  </div>
                </div>

                {/* Title */}
                <div className="p-4">
                  <h3 className="text-white font-medium text-sm leading-tight">
                    {video.title}
                  </h3>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
